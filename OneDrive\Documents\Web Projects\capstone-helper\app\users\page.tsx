import React from "react";
import { UserType } from "@/store/users";

export default async function Users() {
  const res = await fetch("http://localhost:3000/api/users");
  const users: UserType[] = await res.json();

  console.log(users);
  return (
    <div className="h-screen mt-20 container mx-auto gap-4">
      <h1 className="text-5xl font-bold mb-10">Users</h1>
      <div className="flex flex-row gap-4 flex-wrap w-full ">
        {users.map((user) => (
          <div
            key={user.id}
            className="bg-white rounded-lg shadow-lg p-6 w-1/4 h-96"
          >
            <h2 className="text-xl font-bold mb-4">{user.name}</h2>
            <p className="text-gray-600 mb-4">{user.email}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
